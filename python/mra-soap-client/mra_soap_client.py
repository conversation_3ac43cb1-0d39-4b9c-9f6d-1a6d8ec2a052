#!/usr/bin/env python3
"""
MRA SOAP Client for Air Cargo Malawi

This module provides a SOAP client to connect to the MRA SOAP service
and push XML data for different message types (MAWB, XFFM, XFZB).
"""

import logging
import os
import xml.etree.ElementTree as ET
from datetime import datetime, timezone
from typing import Any, Dict, Optional, Tuple

import requests
from requests.adapters import HTTPAdapter
from requests.auth import HTTPBasicAuth
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Set up debug logger for XFFM submissions
debug_logger = logging.getLogger("mra_xffm_debug")
debug_logger.setLevel(logging.DEBUG)

# Create debug log file handler if not already exists
if not debug_logger.handlers:
    debug_log_path = "/var/www/cargo-mis/logs/mra-xffm-submissions.log"
    os.makedirs(os.path.dirname(debug_log_path), exist_ok=True)

    debug_handler = logging.FileHandler(debug_log_path)
    debug_handler.setLevel(logging.DEBUG)

    # Create detailed formatter for debug logs
    debug_formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )
    debug_handler.setFormatter(debug_formatter)
    debug_logger.addHandler(debug_handler)
    debug_logger.propagate = False


class MRASOAPClient:
    """
    SOAP client for connecting to MRA SOAP service and pushing XML data.
    """

    def __init__(
        self,
        wsdl_url: str = "http://asyw-prototype.mra.mw:8084/asyws3iata/WSIATACargoXML?wsdl",
        username: str = "AC.WS",
        password: str = "iata@123",
        timeout: int = 30,
        max_retries: int = 3,
        file_mode: bool = False,
        output_dir: str = "/var/www/cargo-mis/mra-output",
    ):
        """
        Initialize the MRA SOAP client.

        Args:
            wsdl_url: MRA SOAP service WSDL URL
            username: Basic auth username
            password: Basic auth password
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            file_mode: If True, write to files instead of sending to MRA
            output_dir: Directory to write files when in file mode
        """
        self.wsdl_url = wsdl_url
        self.soap_url = wsdl_url.replace("?wsdl", "")
        self.username = username
        self.password = password
        self.timeout = timeout
        self.max_retries = max_retries

        # File mode configuration
        self.file_mode = (
            file_mode or os.getenv("MRA_FILE_MODE", "false").lower() == "true"
        )
        self.output_dir = output_dir or os.getenv(
            "MRA_OUTPUT_DIR", "/var/www/cargo-mis/mra-output"
        )

        # Create output directory structure if in file mode
        if self.file_mode:
            os.makedirs(self.output_dir, exist_ok=True)
            os.makedirs(os.path.join(self.output_dir, "xffm"), exist_ok=True)
            os.makedirs(os.path.join(self.output_dir, "mawb"), exist_ok=True)
            os.makedirs(
                os.path.join(self.output_dir, "xfwb"), exist_ok=True
            )  # Added for XFWB
            os.makedirs(os.path.join(self.output_dir, "xfzb"), exist_ok=True)
            logger.info(f"File mode enabled - output directory: {self.output_dir}")

        # Setup session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"],  # Updated parameter name
            backoff_factor=1,
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Set authentication
        self.session.auth = HTTPBasicAuth(username, password)

        if self.file_mode:
            logger.info(
                f"Initialized MRA SOAP client in FILE MODE - output: {self.output_dir}"
            )
        else:
            logger.info(f"Initialized MRA SOAP client for {self.soap_url}")

    def _create_soap_envelope(
        self, method_name: str, xml_data: str, airline_code: str = "AC"
    ) -> str:
        """
        Create SOAP envelope for the request.

        Args:
            method_name: SOAP method name (sendMessageXFFM, sendMessageXFWB, sendMessageXFZB)
            xml_data: Raw XML data to send (may be JSON-wrapped or raw XML)
            airline_code: Airline code (default: AC for Air Cargo)

        Returns:
            Complete SOAP envelope as string
        """
        # Map method names to correct parameter names for ASYCUDA API
        param_mapping = {
            "sendMessageXFFM": "xffm",
            "sendMessageXFWB": "xfwb",
            "sendMessageXFZB": "xfzb",
        }

        xml_param = param_mapping.get(method_name, "xmlData")

        # Extract actual XML if it's in JSON format
        actual_xml = xml_data
        if isinstance(xml_data, dict) and "xml" in xml_data:
            actual_xml = xml_data["xml"]
        elif isinstance(xml_data, str):
            try:
                import json

                parsed = json.loads(xml_data)
                if isinstance(parsed, dict) and "xml" in parsed:
                    actual_xml = parsed["xml"]
            except:
                pass  # Use original xml_data as-is

        # Remove XML declaration from actual_xml if present
        clean_xml_data = actual_xml
        if actual_xml.strip().startswith("<?xml"):
            # Find the end of the XML declaration
            xml_decl_end = actual_xml.find("?>") + 2
            if xml_decl_end > 1:  # Found the declaration
                clean_xml_data = actual_xml[xml_decl_end:].strip()

        # Create SOAP envelope without XML declaration
        # Use CDATA to preserve XML structure without escaping
        soap_envelope = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:api3="http://www.asycuda.org/api3">
    <soapenv:Header/>
    <soapenv:Body>
        <api3:{method_name}>
            <api3:airline>{airline_code}</api3:airline>
            <api3:{xml_param}><![CDATA[{clean_xml_data}]]></api3:{xml_param}>
        </api3:{method_name}>
    </soapenv:Body>
</soapenv:Envelope>"""

        return soap_envelope

    def _extract_airline_code_from_xml(self, xml_data: str, message_type: str) -> str:
        """
        Extract airline code from XML data.

        Args:
            xml_data: Raw XML data
            message_type: Type of message (XFFM, XFWB, XFZB)

        Returns:
            Extracted airline code or 'AC' as default
        """
        try:
            # Extract actual XML if it's in JSON format
            actual_xml = xml_data
            if isinstance(xml_data, dict) and "xml" in xml_data:
                actual_xml = xml_data["xml"]
            elif isinstance(xml_data, str):
                try:
                    import json

                    parsed = json.loads(xml_data)
                    if isinstance(parsed, dict) and "xml" in parsed:
                        actual_xml = parsed["xml"]
                except:
                    pass  # Use original xml_data

            # Parse XML to extract airline code
            root = ET.fromstring(actual_xml)

            # Common airline code extraction patterns
            airline_patterns = [
                # IATA Cargo XML patterns
                ".//{*}CarrierParty/{*}PrimaryID",
                ".//{*}Carrier/{*}AirlineID",
                ".//{*}AirlineID",
                ".//{*}CarrierCode",
                # Flight manifest patterns
                ".//{*}FlightNumber",
                ".//{*}Flight/{*}CarrierCode",
                # Waybill patterns
                ".//{*}AWBNumber",
                ".//{*}WaybillNumber",
            ]

            for pattern in airline_patterns:
                elements = root.findall(pattern)
                for elem in elements:
                    if elem.text:
                        # Extract airline code from various formats
                        text = elem.text.strip()

                        # If it's a flight number (e.g., "KQ775"), extract airline code
                        if len(text) > 2 and text[:2].isalpha():
                            airline_code = text[:2].upper()
                            if airline_code.isalpha():
                                logger.info(
                                    f"Extracted airline code '{airline_code}' from {message_type} XML"
                                )
                                return airline_code

                        # If it's an AWB number (e.g., "706-12345678"), extract airline code
                        if "-" in text and len(text.split("-")[0]) == 3:
                            airline_code = text.split("-")[0]
                            if airline_code.isdigit():
                                # Convert numeric airline code to IATA code if needed
                                # This is a simplified mapping - in production you'd have a full lookup table
                                numeric_to_iata = {
                                    "706": "AC",  # Air Cargo Malawi
                                    "180": "KQ",  # Kenya Airways
                                    "071": "ET",  # Ethiopian Airlines
                                    "176": "EK",  # Emirates
                                }
                                if airline_code in numeric_to_iata:
                                    iata_code = numeric_to_iata[airline_code]
                                    logger.info(
                                        f"Converted numeric airline code '{airline_code}' to IATA code '{iata_code}' from {message_type} XML"
                                    )
                                    return iata_code

                        # If it's already a 2-letter IATA code
                        if len(text) == 2 and text.isalpha():
                            logger.info(
                                f"Found airline code '{text.upper()}' from {message_type} XML"
                            )
                            return text.upper()

            logger.warning(
                f"Could not extract airline code from {message_type} XML, using default 'AC'"
            )
            return "AC"

        except Exception as e:
            logger.warning(
                f"Error extracting airline code from {message_type} XML: {e}, using default 'AC'"
            )
            return "AC"

    def _log_submission_debug(
        self,
        soap_envelope: str,
        xml_data: str,
        airline_code: str,
        method_name: str,
        message_type: str,
    ):
        """
        Log detailed debug information for MRA submissions.

        Args:
            soap_envelope: Complete SOAP envelope being sent
            xml_data: Raw XML data being submitted
            airline_code: Airline code being used
            method_name: SOAP method name
            message_type: Type of message (XFFM, XFWB, XFZB)
        """
        try:
            timestamp = datetime.now(timezone.utc).isoformat()

            debug_logger.info("=" * 80)
            debug_logger.info(f"{message_type} SUBMISSION DEBUG LOG - {timestamp}")
            debug_logger.info("=" * 80)
            debug_logger.info(f"Method: {method_name}")
            debug_logger.info(f"Message Type: {message_type}")
            debug_logger.info(f"Airline Code: {airline_code}")
            debug_logger.info(f"Endpoint: {self.soap_url}")

            # Extract actual XML if it's in JSON format
            actual_xml = xml_data
            if isinstance(xml_data, dict) and "xml" in xml_data:
                actual_xml = xml_data["xml"]
            elif isinstance(xml_data, str):
                try:
                    import json

                    parsed = json.loads(xml_data)
                    if isinstance(parsed, dict) and "xml" in parsed:
                        actual_xml = parsed["xml"]
                except:
                    pass  # Use original xml_data

            debug_logger.info(f"XML Data Size: {len(str(actual_xml))} characters")
            debug_logger.info("-" * 40)
            debug_logger.info("COMPLETE SOAP ENVELOPE:")
            debug_logger.info("-" * 40)
            debug_logger.info(soap_envelope)
            debug_logger.info("-" * 40)
            debug_logger.info("RAW XML DATA:")
            debug_logger.info("-" * 40)
            debug_logger.info(actual_xml)
            debug_logger.info("=" * 80)
            debug_logger.info(f"END OF {message_type} SUBMISSION DEBUG LOG")
            debug_logger.info("=" * 80)
            debug_logger.info("")  # Empty line for separation

        except Exception as e:
            logger.warning(f"Error writing {message_type} debug log: {e}")

    def _log_xffm_debug(
        self, soap_envelope: str, xml_data: str, airline_code: str, method_name: str
    ):
        """
        Log detailed debug information for XFFM submissions.
        Maintained for backward compatibility.
        """
        self._log_submission_debug(
            soap_envelope, xml_data, airline_code, method_name, "XFFM"
        )

    def _write_to_file(
        self, xml_data: str, soap_envelope: str, message_type: str, airline_code: str
    ) -> Dict[str, Any]:
        """
        Write XML data and SOAP envelope to files instead of sending to MRA.

        Args:
            xml_data: Raw XML data
            soap_envelope: Complete SOAP envelope
            message_type: Type of message (MAWB, XFFM, XFZB)
            airline_code: Airline code

        Returns:
            Dictionary with file write result
        """
        try:
            timestamp = datetime.now(timezone.utc)
            timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S_%f")[
                :-3
            ]  # Include milliseconds

            # Create subdirectory based on message type
            subdir = message_type.lower()
            type_dir = os.path.join(self.output_dir, subdir)

            # Generate filenames
            xml_filename = f"{airline_code}_{message_type}_{timestamp_str}.xml"
            soap_filename = f"{airline_code}_{message_type}_{timestamp_str}_soap.xml"

            xml_filepath = os.path.join(type_dir, xml_filename)
            soap_filepath = os.path.join(type_dir, soap_filename)

            # Extract actual XML if it's in JSON format
            actual_xml = xml_data
            if isinstance(xml_data, dict) and "xml" in xml_data:
                actual_xml = xml_data["xml"]
            elif isinstance(xml_data, str):
                try:
                    import json

                    parsed = json.loads(xml_data)
                    if isinstance(parsed, dict) and "xml" in parsed:
                        actual_xml = parsed["xml"]
                except:
                    pass  # Use original xml_data as-is

            # Write XML data file (raw XML, not JSON-wrapped)
            with open(xml_filepath, "w", encoding="utf-8") as f:
                f.write(actual_xml)

            # Write SOAP envelope file
            with open(soap_filepath, "w", encoding="utf-8") as f:
                f.write(soap_envelope)

            logger.info(f"Successfully wrote {message_type} files:")
            logger.info(f"  XML: {xml_filepath}")
            logger.info(f"  SOAP: {soap_filepath}")

            # Generate mock XFNM response for testing
            mock_xfnm_content = self._generate_mock_xfnm_response(
                message_type, airline_code
            )

            return {
                "success": True,
                "message": f"Successfully wrote {message_type} to files",
                "xml_file": xml_filepath,
                "soap_file": soap_filepath,
                "timestamp": timestamp.isoformat(),
                "file_mode": True,
                "xfnm_content": mock_xfnm_content,
            }

        except Exception as e:
            error_msg = f"Error writing {message_type} to files: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "error_code": "FILE_WRITE_ERROR",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "file_mode": True,
            }

    def _generate_mock_xfnm_response(self, message_type: str, airline_code: str) -> str:
        """
        Generate a mock XFNM acknowledgment response for testing in file mode.

        Args:
            message_type: Type of message (MAWB, XFFM, XFZB)
            airline_code: Airline code

        Returns:
            Mock XFNM content as XML string
        """
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S")
        conversation_id = (
            f"MOCK_{message_type}_{airline_code}_{int(datetime.now().timestamp())}"
        )

        # Generate different response types for testing
        import random

        response_types = [
            "accepted",
            "accepted",
            "accepted",
            "rejected",
        ]  # 75% acceptance rate
        response_type = random.choice(response_types)

        if response_type == "accepted":
            status_code = "Accepted"
            condition_code = "Success"
            reason = "Message processed successfully by MRA ASYCUDA system"
        else:
            status_code = "Rejected"
            condition_code = "Error"
            reasons = [
                "Invalid AWB number format",
                "Duplicate submission detected",
                "Missing required field: ConsigneeParty",
                "Weight discrepancy detected",
            ]
            reason = random.choice(reasons)

        mock_xfnm = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response xmlns="iata:response:1" xmlns:ram="iata:datamodel:4">
    <BusinessHeaderDocument>
        <ram:ID>{conversation_id}</ram:ID>
        <ram:Name>XFNM Acknowledgment</ram:Name>
        <ram:TypeCode>XFNM</ram:TypeCode>
        <ram:IssueDateTime>{timestamp}</ram:IssueDateTime>
        <ram:StatusCode>{status_code}</ram:StatusCode>
        <ram:SenderParty>
            <ram:PrimaryID>MRA_ASYCUDA</ram:PrimaryID>
        </ram:SenderParty>
        <ram:RecipientParty>
            <ram:PrimaryID>{airline_code}</ram:PrimaryID>
        </ram:RecipientParty>
    </BusinessHeaderDocument>
    <ResponseStatus>
        <ConditionCode>{condition_code}</ConditionCode>
        <Reason>{reason}</Reason>
        <ProcessingDateTime>{timestamp}</ProcessingDateTime>
    </ResponseStatus>
</Response>"""

        logger.info(f"Generated mock XFNM response: {status_code} - {condition_code}")
        return mock_xfnm

    def _parse_iata_response(
        self, root: ET.Element
    ) -> Optional[Tuple[bool, str, Optional[str]]]:
        """
        Parse IATA Response format from MRA.

        Expected format:
        <Response xmlns="iata:response:1">
            <BusinessHeaderDocument>
                <StatusCode>Accepted|Rejected</StatusCode>
            </BusinessHeaderDocument>
            <ResponseStatus>
                <ConditionCode>Success|Error</ConditionCode>
                <Reason>Error details if any</Reason>
            </ResponseStatus>
        </Response>

        Args:
            root: XML root element

        Returns:
            Tuple of (success, message, error_code) or None if not IATA format
        """
        try:
            # Look for IATA Response element (could be in SOAP body or as CDATA)
            iata_response = None

            # First check if it's directly in the response
            for elem in root.iter():
                if elem.tag.endswith("}Response") or elem.tag == "Response":
                    # Check if it has the IATA namespace
                    if (
                        "iata:response" in str(elem.tag)
                        or elem.get("xmlns") == "iata:response:1"
                    ):
                        iata_response = elem
                        break

            # If not found directly, look for it in CDATA or text content
            if iata_response is None:
                for elem in root.iter():
                    if (
                        elem.text
                        and "<?xml" in elem.text
                        and "iata:response" in elem.text
                    ):
                        try:
                            # Parse the embedded XML
                            embedded_xml = elem.text.strip()
                            if embedded_xml.startswith("&lt;"):
                                # Unescape HTML entities
                                embedded_xml = (
                                    embedded_xml.replace("&lt;", "<")
                                    .replace("&gt;", ">")
                                    .replace("&amp;", "&")
                                )
                            iata_response = ET.fromstring(embedded_xml)
                            break
                        except ET.ParseError:
                            continue

            if iata_response is not None:
                # Extract StatusCode from BusinessHeaderDocument using simpler approach
                status_code = None
                condition_code = None
                reason = None

                # Iterate through all elements to find what we need
                for elem in iata_response.iter():
                    tag_name = elem.tag.split("}")[-1] if "}" in elem.tag else elem.tag

                    if tag_name == "StatusCode" and elem.text:
                        status_code = elem.text
                    elif tag_name == "ConditionCode" and elem.text:
                        condition_code = elem.text
                    elif tag_name == "Reason" and elem.text:
                        reason = elem.text

                # Determine success based on StatusCode and ConditionCode
                is_success = False
                message = "Unknown response"
                error_code = None

                if status_code == "Accepted" and condition_code == "Success":
                    is_success = True
                    message = "MRA accepted the submission successfully"
                elif status_code == "Rejected":
                    is_success = False
                    message = (
                        f"MRA rejected the submission: {reason or 'No reason provided'}"
                    )
                    error_code = "MRA_REJECTED"
                elif condition_code == "Error":
                    is_success = False
                    message = f"MRA reported an error: {reason or 'No reason provided'}"
                    error_code = "MRA_ERROR"
                else:
                    # Partial success or unknown status
                    is_success = True
                    message = f"MRA response - Status: {status_code or 'Unknown'}, Condition: {condition_code or 'Unknown'}"
                    if reason:
                        message += f", Reason: {reason}"

                return (is_success, message, error_code)

            return None  # Not an IATA response format

        except Exception as e:
            logger.warning(f"Error parsing IATA response: {e}")
            return None

    def _extract_xfnm_content(self, root: ET.Element) -> Optional[str]:
        """
        Extract XFNM acknowledgment content from SOAP response.

        Args:
            root: XML root element

        Returns:
            XFNM content as string or None if not found
        """
        try:
            # Look for XFNM content in various possible locations
            xfnm_elements = [
                ".//{http://www.asycuda.org/api3}xfnm",
                ".//xfnm",
                ".//*[local-name()='xfnm']",
            ]

            for xpath in xfnm_elements:
                xfnm_elem = root.find(xpath)
                if xfnm_elem is not None and xfnm_elem.text:
                    # Clean up the XFNM content
                    xfnm_text = xfnm_elem.text.strip()

                    # If it's wrapped in CDATA, it might have HTML entities
                    if "&lt;" in xfnm_text:
                        xfnm_text = (
                            xfnm_text.replace("&lt;", "<")
                            .replace("&gt;", ">")
                            .replace("&amp;", "&")
                        )

                    return xfnm_text

            return None

        except Exception as e:
            logger.warning(f"Error extracting XFNM content: {e}")
            return None

    def _parse_soap_response(
        self, response_text: str
    ) -> Tuple[bool, str, Optional[str], Optional[str]]:
        """
        Parse SOAP response and extract result.
        Handles both ASYCUDA format and IATA Response format.

        Args:
            response_text: Raw SOAP response text

        Returns:
            Tuple of (success, message, error_code, xfnm_content)
        """
        try:
            # Parse XML response
            root = ET.fromstring(response_text)
            xfnm_content = None

            # Look for SOAP fault first
            fault = root.find(".//{http://schemas.xmlsoap.org/soap/envelope/}Fault")
            if fault is not None:
                fault_code = fault.find(".//faultcode")
                fault_string = fault.find(".//faultstring")
                error_msg = (
                    f"SOAP Fault - Code: {fault_code.text if fault_code is not None else 'Unknown'}, "
                    f"Message: {fault_string.text if fault_string is not None else 'Unknown error'}"
                )
                return (
                    False,
                    error_msg,
                    fault_code.text if fault_code is not None else None,
                    None,
                )

            # Check for IATA Response format first (inside SOAP body)
            iata_response = self._parse_iata_response(root)
            if iata_response is not None:
                # Extract XFNM content if available
                xfnm_content = self._extract_xfnm_content(root)
                return iata_response + (xfnm_content,)

            # Look for response body
            body = root.find(".//{http://schemas.xmlsoap.org/soap/envelope/}Body")
            if body is not None:
                # Extract XFNM content first
                xfnm_content = self._extract_xfnm_content(root)

                # Look for ASYCUDA API response elements using simpler approach
                try:
                    # Try to find sendMessageXFFMResponse or similar
                    for elem in body.iter():
                        tag_name = (
                            elem.tag.split("}")[-1] if "}" in elem.tag else elem.tag
                        )
                        if tag_name in [
                            "sendMessageXFFMResponse",
                            "sendMessageXFWBResponse",
                            "sendMessageXFZBResponse",
                        ]:
                            # Found response element, look for child data
                            for child in elem:
                                child_tag = (
                                    child.tag.split("}")[-1]
                                    if "}" in child.tag
                                    else child.tag
                                )
                                if child_tag in ["xfnm", "xfwb", "xfzb"] and child.text:
                                    return (
                                        True,
                                        f"MRA processed {tag_name} successfully",
                                        None,
                                        xfnm_content,
                                    )
                            # Even if no child data, response element exists
                            return (
                                True,
                                f"MRA processed {tag_name} successfully",
                                None,
                                xfnm_content,
                            )

                    # Look for generic return element
                    for elem in body.iter():
                        tag_name = (
                            elem.tag.split("}")[-1] if "}" in elem.tag else elem.tag
                        )
                        if tag_name == "return" and elem.text:
                            return True, elem.text or "Success", None, xfnm_content

                except Exception as e:
                    logger.warning(f"Error parsing response elements: {e}")
                    # Fall back to basic success if we got this far without SOAP fault
                    return (
                        True,
                        "MRA response received (parsing issue)",
                        None,
                        xfnm_content,
                    )

                # If no specific response element found, consider it successful if no fault
                return True, "Request processed successfully", None, xfnm_content

            return False, "Invalid SOAP response format", "INVALID_RESPONSE", None

        except ET.ParseError as e:
            return False, f"Invalid XML response: {str(e)}", "XML_PARSE_ERROR", None
        except Exception as e:
            return False, f"Error parsing response: {str(e)}", "PARSE_ERROR", None

    def send_xml_to_mra(
        self, xml_data: str, message_type: str, airline_code: str = "AC"
    ) -> Dict[str, Any]:
        """
        Send XML data to MRA via SOAP.

        Args:
            xml_data: Raw XML data to send
            message_type: Type of message (MAWB, XFFM, XFZB)
            airline_code: Airline code (default: AC)

        Returns:
            Dictionary with submission result
        """
        # Map message types to SOAP methods
        method_mapping = {
            "MAWB": "sendMessageXFWB",
            "XFFM": "sendMessageXFFM",
            "XFZB": "sendMessageXFZB",
        }

        if message_type not in method_mapping:
            return {
                "success": False,
                "error": f"Unsupported message type: {message_type}",
                "error_code": "INVALID_MESSAGE_TYPE",
                "response_data": None,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        method_name = method_mapping[message_type]

        try:
            # Standardize the XML payload: unwrap from JSON if necessary.
            # This ensures that a pure XML string is used for envelope creation,
            # logging, and file writing, matching the required standard.
            raw_xml_payload = xml_data
            if isinstance(xml_data, str) and xml_data.strip().startswith("{"):
                try:
                    import json

                    parsed_data = json.loads(xml_data)
                    if isinstance(parsed_data, dict) and "xml" in parsed_data:
                        raw_xml_payload = parsed_data["xml"]
                except (ImportError, json.JSONDecodeError):
                    # If parsing fails, assume it's not a JSON wrapper and proceed.
                    pass

            # Extract airline code from XML if not provided or if default
            if airline_code == "AC" or not airline_code:
                extracted_airline_code = self._extract_airline_code_from_xml(
                    raw_xml_payload, message_type
                )
                if extracted_airline_code != "AC":
                    airline_code = extracted_airline_code
                    logger.info(f"Using extracted airline code: {airline_code}")

            # Create SOAP envelope
            soap_envelope = self._create_soap_envelope(
                method_name, raw_xml_payload, airline_code
            )

            # Prepare headers for ASYCUDA API
            headers = {
                "Content-Type": "text/xml; charset=utf-8",
                "SOAPAction": f'"http://www.asycuda.org/api3/{method_name}"',
                "Accept": "text/xml",
            }

            logger.info(f"Sending {message_type} message to MRA via {method_name}")

            # Debug logging for all submission types
            if message_type in ["XFFM", "MAWB", "XFZB"]:
                self._log_submission_debug(
                    soap_envelope,
                    raw_xml_payload,
                    airline_code,
                    method_name,
                    message_type,
                )

            # Check if we're in file mode
            if self.file_mode:
                logger.info(
                    f"File mode enabled - writing {message_type} to files instead of sending to MRA"
                )
                return self._write_to_file(
                    raw_xml_payload, soap_envelope, message_type, airline_code
                )

            # Send SOAP request to MRA
            response = self.session.post(
                self.soap_url, data=soap_envelope, headers=headers, timeout=self.timeout
            )

            # Check HTTP status
            response.raise_for_status()

            # Parse SOAP response
            success, message, error_code, xfnm_content = self._parse_soap_response(
                response.text
            )

            result = {
                "success": success,
                "message": message,
                "error_code": error_code,
                "response_data": response.text,
                "xfnm_content": xfnm_content,
                "http_status": response.status_code,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            if success:
                logger.info(f"Successfully sent {message_type} to MRA: {message}")
            else:
                logger.error(f"Failed to send {message_type} to MRA: {message}")

            return result

        except requests.exceptions.Timeout:
            error_msg = f"Timeout while sending {message_type} to MRA"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "error_code": "TIMEOUT",
                "response_data": None,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except requests.exceptions.ConnectionError:
            error_msg = f"Connection error while sending {message_type} to MRA"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "error_code": "CONNECTION_ERROR",
                "response_data": None,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP error while sending {message_type} to MRA: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "error_code": f"HTTP_{response.status_code}",
                "response_data": response.text if "response" in locals() else None,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            error_msg = (
                f"Unexpected error while sending {message_type} to MRA: {str(e)}"
            )
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "error_code": "UNEXPECTED_ERROR",
                "response_data": None,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    def test_connection(self) -> Dict[str, Any]:
        """
        Test connection to MRA SOAP service by fetching WSDL.

        Returns:
            Dictionary with connection test result
        """
        try:
            logger.info("Testing connection to MRA SOAP service...")

            response = self.session.get(self.wsdl_url, timeout=self.timeout)
            response.raise_for_status()

            # Check if response contains WSDL content
            if "wsdl:definitions" in response.text or "definitions" in response.text:
                logger.info("Successfully connected to MRA SOAP service")
                return {
                    "success": True,
                    "message": "Connection successful",
                    "wsdl_available": True,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            else:
                logger.warning("Connected to MRA service but WSDL format unexpected")
                return {
                    "success": False,
                    "message": "Connected but WSDL format unexpected",
                    "wsdl_available": False,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

        except Exception as e:
            error_msg = f"Failed to connect to MRA SOAP service: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "wsdl_available": False,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }


if __name__ == "__main__":
    # Simple test when run directly
    client = MRASOAPClient()
    result = client.test_connection()
    print(f"Connection test result: {result}")
